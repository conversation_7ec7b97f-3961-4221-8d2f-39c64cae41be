---
puppeteer:
  displayHeaderFooter: true
  headerTemplate: "<div style='width: 100%; height: 0;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "1cm"
    bottom: "1.5cm"
    left: "1cm"
  format: "A4"
  printBackground: true
---

# FDN、MOC、BPN バージョンアップ計画書

## 目次
1. [現状システム構成](#1-現状システム構成)
2. [現状の課題とリスク](#2-現状の課題とリスク)
3. [バージョンアップ方針](#3-バージョンアップ方針)
4. [具体的なバージョンアップ計画](#4-具体的なバージョンアップ計画)
5. [リスクと対策](#5-リスクと対策)
6. [今後の課題](#6-今後の課題)

## 1. 現状システム構成

### 1.1 システム概要
- **システム名**: FDN、MOC、BPNシステム
- **特徴**: 業務機能が最も大規模なシステム群

### 1.2 技術スタック
- **OS**: CentOS 7
- **言語**: PHP 7
- **PHPフレームワーク**: Zend Framework
- **JavaScriptフレームワーク**: Backbone.js
- **データベース**: PostgreSQL 10

## 2. 現状の課題とリスク

### 2.1 セキュリティリスク
- **CentOS 7**: 2024年6月30日でEOL（End of Life）済み
- **PHP 7**: 2022年11月28日でセキュリティサポート終了済み
- **PostgreSQL 10**: 2022年11月10日でEOL済み

### 2.2 技術的負債
- 古いバージョンによる新機能の利用不可
- セキュリティパッチの提供停止
- 開発・運用効率の低下
- Zend FrameworkとBackbone.jsの保守性低下

## 3. バージョンアップ方針

### 3.1 基本方針
最低限必要なセキュリティサポート対象バージョンへの更新を優先し、システムの安定性を保ちながら段階的にアップグレードを実施する。

### 3.2 フレームワーク更新方針
以下のフレームワークについては、技術的制約により**更新は実施しない**：

- **Zend Framework**: 2018年にLaminas Projectに移行済み、移行は影響範囲が膨大
- **Backbone.js**: 開発が事実上停止、React/Vue.js等への移行が必要

これらのフレームワーク更新は、システム全体のリプレースプロジェクトとして別途検討が必要。

## 4. 具体的なバージョンアップ計画

**注意**: 以下の想定期間は概算であり、実際の作業では要件の詳細化、予期しない技術的課題、テスト結果等により期間が変動する可能性があります。

### 4.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9（サポート期限: 2032年5月31日）
- **PHP**: PHP 7 → PHP 8.3 LTS（サポート期限: 2026年11月23日）
- **PHPフレームワーク**: Zend Framework → **更新なし**（制約により）
- **JavaScriptフレームワーク**: Backbone.js → **更新なし**（制約により）
- **PostgreSQL**: PostgreSQL 10 → PostgreSQL 16（サポート期限: 2028年11月9日）

### 4.2 実施手順
1. **Phase 1**: PostgreSQL 10 → 16 アップグレード
2. **Phase 2**: PHP 7 → 8.3 アップグレード（Zend Framework互換性検証含む）
3. **Phase 3**: OS移行（CentOS 7 → AlmaLinux 9）

**注意**: 業務機能が大規模なため、Zend FrameworkとBackbone.jsの互換性確認が重要。特にPHP 8.3での動作検証を十分に実施する。

### 4.3 想定期間
- バージョンアップ期間: 想定2ヶ月（PostgreSQL、PHP環境構築）
- 改修期間: 想定3ヶ月（Zend Framework、Backbone.js互換性対応）
- 検証・テスト期間: 想定1ヶ月（業務機能が大規模なため十分な検証）
- 本番適用期間: 想定2週間
- **合計**: 想定6ヶ月

**注意**: 実際の期間は技術的課題や業務要件により変動する可能性があります。

### 4.4 詳細スケジュール

#### Phase 1: PostgreSQL 10 → 16 アップグレード（想定2ヶ月）
- **Week 1-2**: 環境構築・バックアップ戦略策定
- **Week 3-4**: PostgreSQL 16インストール・設定
- **Week 5-6**: データ移行テスト
- **Week 7-8**: 性能検証・チューニング

#### Phase 2: PHP 7 → 8.3 アップグレード（想定3ヶ月）
- **Month 1**: PHP 8.3環境構築
- **Month 2**: Zend Framework互換性検証・修正
- **Month 3**: Backbone.js連携部分の検証・修正

#### Phase 3: OS移行（想定1ヶ月）
- **Week 1-2**: AlmaLinux 9環境構築
- **Week 3-4**: アプリケーション移行・最終検証

## 5. リスクと対策

### 5.1 主要リスク
1. **Zend Framework互換性問題**
   - PHP 8.3での非互換性
   - 廃止予定機能の使用
2. **Backbone.js互換性問題**
   - 古いJavaScript環境での動作不良
3. **PostgreSQL大幅バージョンアップ**
   - データ移行時の不整合
   - 性能劣化
4. **業務機能の複雑性**
   - 大規模システムでの予期しない影響

### 5.2 対策
1. **十分な検証環境での事前テスト**
2. **段階的なロールバック計画の策定**
3. **専門技術者によるコードレビュー**
4. **性能監視とチューニング**

## 6. 今後の課題

### 6.1 フレームワーク刷新の必要性
中長期的には以下の対応が必要：

- **Zend Framework**: Laminas Projectへの移行またはLaravel等への技術刷新
- **Backbone.js**: React、Vue.js、Angular等のモダンJavaScriptフレームワークへの移行

### 6.2 技術刷新プロジェクトの検討
フレームワーク更新を含む抜本的なシステム刷新について、別途プロジェクトとして検討することを推奨。

<!-- ---

**作成日**: 2025年7月22日  
**更新日**: -  
**承認者**: - -->
