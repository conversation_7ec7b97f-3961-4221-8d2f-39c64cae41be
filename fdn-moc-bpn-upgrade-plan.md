---
puppeteer:
  displayHeaderFooter: true
  headerTemplate: "<div style='width: 100%; height: 0;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "1cm"
    bottom: "1.5cm"
    left: "1cm"
  format: "A4"
  printBackground: true
---

# FDN、MOC、BPN バージョンアップ計画書

## 目次
1. [現状システム構成](#1-現状システム構成)
2. [現状の課題とリスク](#2-現状の課題とリスク)
3. [バージョンアップ方針](#3-バージョンアップ方針)
4. [具体的なバージョンアップ計画](#4-具体的なバージョンアップ計画)
5. [リスクと対策](#5-リスクと対策)
6. [今後の課題](#6-今後の課題)

## 1. 現状システム構成

### 1.1 システム概要
- **システム名**: FDN、MOC、BPNシステム
- **特徴**: 業務機能が最も大規模なシステム群

### 1.2 技術スタック
- **OS**: CentOS 7
- **言語**: PHP 7
- **PHPフレームワーク**: Zend Framework
- **JavaScriptフレームワーク**: Backbone.js
- **JavaScriptライブラリ**: jQuery 1.10、Underscore.js 1.5
- **データベース**: PostgreSQL 10

## 2. 現状の課題とリスク

### 2.1 セキュリティリスク
- **CentOS 7**: 2024年6月30日でEOL（End of Life）済み
- **PHP 7**: 2022年11月28日でセキュリティサポート終了済み
- **PostgreSQL 10**: 2022年11月10日でEOL済み

### 2.2 技術的負債
- 古いバージョンによる新機能の利用不可
- セキュリティパッチの提供停止
- 開発・運用効率の低下
- Zend FrameworkとBackbone.jsの保守性低下
- jQuery 1.10とUnderscore.js 1.5の古いバージョンによるセキュリティリスク

## 3. バージョンアップ方針

### 3.1 基本方針
最低限必要なセキュリティサポート対象バージョンへの更新を優先し、システムの安定性を保ちながら段階的にアップグレードを実施する。

### 3.2 フレームワーク・ライブラリ更新方針

#### 3.2.1 更新対象
以下のJavaScriptライブラリについては**バージョンアップを実施**する：

- **jQuery**: 1.10 → 3.7（サポート期限: 継続中）
- **Underscore.js**: 1.5 → 1.13（サポート期限: 継続中）

#### 3.2.2 更新しないフレームワーク
以下のフレームワークについては、技術的制約により**更新は実施しない**：

- **Zend Framework**: 2018年にLaminas Projectに移行済み、移行は影響範囲が膨大
- **Backbone.js**: 開発が事実上停止、React/Vue.js等への移行が必要

これらのフレームワーク更新は、システム全体のリプレースプロジェクトとして別途検討が必要。

## 4. 具体的なバージョンアップ計画

**注意**: 以下の想定期間は概算であり、実際の作業では要件の詳細化、予期しない技術的課題、テスト結果等により期間が変動する可能性があります。

### 4.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9（サポート期限: 2032年5月31日）
- **PHP**: PHP 7 → PHP 8.3 LTS（サポート期限: 2026年11月23日）
- **PHPフレームワーク**: Zend Framework → **更新なし**（制約により）
- **JavaScriptフレームワーク**: Backbone.js → **更新なし**（制約により）
- **JavaScriptライブラリ**: jQuery 1.10 → 3.7、Underscore.js 1.5 → 1.13
- **PostgreSQL**: PostgreSQL 10 → PostgreSQL 16（サポート期限: 2028年11月9日）

### 4.2 実施手順
1. **Phase 1**: OS移行（CentOS 7 → AlmaLinux 9）
2. **Phase 2**: PostgreSQL 10 → 16 アップグレード
3. **Phase 3**: PHP 7 → 8.3 アップグレード（Zend Framework、jQuery、Underscore.js互換性検証含む）

**注意**: 業務機能が大規模なため、Zend Framework、Backbone.js、jQuery、Underscore.jsの互換性確認が重要。特にPHP 8.3とjQuery 3.7での動作検証を十分に実施する。

### 4.3 想定期間
- **Phase 1 (OS移行)**: 想定3ヶ月
- **Phase 2 (PostgreSQL)**: 想定3ヶ月
- **Phase 3 (PHP・ライブラリ)**: 想定6ヶ月
- **合計**: 想定12ヶ月

**注意**: 実際の期間は技術的課題や業務要件により変動する可能性があります。

### 4.4 詳細スケジュール

#### Phase 1: OS移行（想定3ヶ月）
- **Month 1**: AlmaLinux 9環境構築・基盤設定
- **Month 2**: アプリケーション移行・基本動作確認
- **Month 3**: 性能検証・チューニング

#### Phase 2: PostgreSQL 10 → 16 アップグレード（想定3ヶ月）
- **Month 1**: 環境構築・バックアップ戦略策定
- **Month 2**: PostgreSQL 16インストール・データ移行テスト
- **Month 3**: 性能検証・チューニング

#### Phase 3: PHP・ライブラリアップグレード（想定6ヶ月）
- **Month 1**: PHP 8.3環境構築
- **Month 2**: jQuery 1.10 → 3.7 移行・検証
- **Month 3**: Underscore.js 1.5 → 1.13 移行・検証
- **Month 4**: Zend Framework互換性検証・修正
- **Month 5**: Backbone.js連携部分の検証・修正
- **Month 6**: 統合テスト・最終検証

## 5. リスクと対策

### 5.1 主要リスク
1. **Zend Framework互換性問題**
   - PHP 8.3での非互換性
   - 廃止予定機能の使用
2. **jQuery大幅バージョンアップ**
   - jQuery 1.10から3.7への大幅変更による互換性問題
   - セレクタやイベント処理の変更
3. **Underscore.js互換性問題**
   - API変更による既存コードへの影響
4. **Backbone.js互換性問題**
   - 古いJavaScript環境での動作不良
5. **PostgreSQL大幅バージョンアップ**
   - データ移行時の不整合
   - 性能劣化
6. **業務機能の複雑性**
   - 大規模システムでの予期しない影響

### 5.2 対策
1. **十分な検証環境での事前テスト**
2. **段階的なロールバック計画の策定**
3. **専門技術者によるコードレビュー**
4. **性能監視とチューニング**

## 6. 今後の課題

### 6.1 フレームワーク刷新の必要性
中長期的には以下の対応が必要：

- **Zend Framework**: Laminas Projectへの移行またはLaravel等への技術刷新
- **Backbone.js**: React、Vue.js、Angular等のモダンJavaScriptフレームワークへの移行

### 6.2 技術刷新プロジェクトの検討
フレームワーク更新を含む抜本的なシステム刷新について、別途プロジェクトとして検討することを推奨。

<!-- ---

**作成日**: 2025年7月22日  
**更新日**: -  
**承認者**: - -->
