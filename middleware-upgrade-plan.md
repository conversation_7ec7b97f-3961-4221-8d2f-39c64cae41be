# ミドルウェアバージョンアップ計画書

## 目次
1. [現状システム構成一覧](#1-現状システム構成一覧)
2. [現状の課題とリスク](#2-現状の課題とリスク)
3. [バージョンアップ方針](#3-バージョンアップ方針)
4. [具体的なバージョンアップ計画](#4-具体的なバージョンアップ計画)
5. [全体スケジュール](#5-全体スケジュール)
6. [リスクと対策](#6-リスクと対策)
7. [今後の課題](#7-今後の課題)

## 1. 現状システム構成一覧

### 1.1 FDN、MOC、BPNシステム
- **OS**: CentOS 7
- **言語**: PHP 7
- **PHPフレームワーク**: Zend Framework
- **JavaScriptフレームワーク**: Backbone.js
- **データベース**: PostgreSQL 10

### 1.2 FOCシステム
- **OS**: CentOS 7
- **言語**: Python 3.8
- **Pythonフレームワーク**: Django 3.1
- **JavaScriptフレームワーク**: Angular 10
- **データベース**: PostgreSQL 12

### 1.3 きよらかシステム
- **OS**: CentOS 7
- **言語**: PHP 7
- **フレームワーク**: Symfony 3.4
- **データベース**: PostgreSQL 9

## 2. 現状の課題とリスク

### 2.1 セキュリティリスク
- **CentOS 7**: 2024年6月30日でEOL（End of Life）済み
- **PHP 7**: 2022年11月28日でセキュリティサポート終了済み
- **PostgreSQL 9**: 2021年11月11日でEOL済み
- **PostgreSQL 10**: 2022年11月10日でEOL済み
- **PostgreSQL 12**: 2024年11月14日でEOL済み

### 2.2 技術的負債
- 古いバージョンによる新機能の利用不可
- セキュリティパッチの提供停止
- 開発・運用効率の低下

## 3. バージョンアップ方針

### 3.1 基本方針
最低限必要なセキュリティサポート対象バージョンへの更新を優先し、システムの安定性を保ちながら段階的にアップグレードを実施する。

### 3.2 フレームワーク更新方針

#### 3.2.1 更新対象フレームワーク
以下のフレームワークについては、セキュリティサポートの観点から**バージョンアップを実施**する：

- **Django 3.1**: Django 4.2 LTS（サポート期限: 2026年4月）への移行
- **Symfony 3.4**: Symfony 6.4 LTS（サポート期限: 2027年11月）への移行

#### 3.2.2 更新しないフレームワーク
以下のフレームワークについては、後続バージョンが存在しないか、技術的制約により**更新は実施しない**：

- **Zend Framework**: 2018年にLaminas Projectに移行済み、移行は影響範囲が膨大
- **Backbone.js**: 開発が事実上停止、React/Vue.js等への移行が必要
- **Angular 10**: Angular 18等への移行は影響範囲が膨大

これらのフレームワーク更新は、システム全体のリプレースプロジェクトとして別途検討が必要。

## 4. 具体的なバージョンアップ計画

**注意**: 以下の想定期間は概算であり、実際の作業では要件の詳細化、予期しない技術的課題、テスト結果等により期間が変動する可能性があります。

### 4.1 FDN、MOC、BPNシステム

#### 4.1.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9（サポート期限: 2032年5月31日）
- **PHP**: PHP 7 → PHP 8.3 LTS（サポート期限: 2026年11月23日）
- **PHPフレームワーク**: Zend Framework → **更新なし**（制約により）
- **JavaScriptフレームワーク**: Backbone.js → **更新なし**（制約により）
- **PostgreSQL**: PostgreSQL 10 → PostgreSQL 16（サポート期限: 2028年11月9日）

#### 4.1.2 実施手順
1. **Phase 1**: PostgreSQL 10 → 16 アップグレード
2. **Phase 2**: PHP 7 → 8.3 アップグレード（Zend Framework互換性検証含む）
3. **Phase 3**: OS移行（CentOS 7 → AlmaLinux 9）

**注意**: 業務機能が大規模なため、Zend FrameworkとBackbone.jsの互換性確認が重要。特にPHP 8.3での動作検証を十分に実施する。

#### 4.1.3 想定期間
- バージョンアップ期間: 想定2ヶ月（PostgreSQL、PHP環境構築）
- 改修期間: 想定3ヶ月（Zend Framework、Backbone.js互換性対応）
- 検証・テスト期間: 想定1ヶ月（業務機能が大規模なため十分な検証）
- 本番適用期間: 想定2週間
- **合計**: 想定6ヶ月

**注意**: 実際の期間は技術的課題や業務要件により変動する可能性があります。

### 4.2 FOCシステム

#### 4.2.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9（サポート期限: 2032年5月31日）
- **Python**: Python 3.8 → Python 3.12（サポート期限: 2028年10月）
- **Pythonフレームワーク**: Django 3.1 → Django 4.2 LTS（サポート期限: 2026年4月）
- **JavaScriptフレームワーク**: Angular 10 → **更新なし**（制約により）
- **PostgreSQL**: PostgreSQL 12 → PostgreSQL 16（サポート期限: 2028年11月9日）

#### 4.2.2 実施手順
1. **Phase 1**: PostgreSQL 12 → 16 アップグレード
2. **Phase 2**: Python 3.8 → 3.12 アップグレード
3. **Phase 3**: Django 3.1 → 4.2 LTS アップグレード（大幅な変更のため慎重に実施）
4. **Phase 4**: OS移行（CentOS 7 → AlmaLinux 9）

**注意**: Django 3.1から4.2への移行は破壊的変更を含むため、十分な検証期間を確保する。Angular 10の互換性確認も重要。

#### 4.2.3 想定期間
- バージョンアップ期間: 想定2ヶ月（PostgreSQL、Python環境構築）
- 改修期間: 想定3ヶ月（Django 3.1→4.2移行対応、Angular 10互換性確認）
- 検証・テスト期間: 想定1ヶ月（Django大幅アップグレードのため十分な検証）
- 本番適用期間: 想定2週間
- **合計**: 想定6ヶ月

**注意**: 実際の期間は技術的課題や業務要件により変動する可能性があります。

### 4.3 きよらかシステム

#### 4.3.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9（サポート期限: 2032年5月31日）
- **PHP**: PHP 7 → PHP 8.3 LTS（サポート期限: 2026年11月23日）
- **Symfony**: 3.4 → Symfony 6.4 LTS（サポート期限: 2027年11月）
- **PostgreSQL**: PostgreSQL 9 → PostgreSQL 16（サポート期限: 2028年11月9日）

#### 4.3.2 実施手順
1. **Phase 1**: PostgreSQL 9 → 16 アップグレード（メジャーバージョンアップのため慎重に実施）
2. **Phase 2**: PHP 7 → 8.3 アップグレード
3. **Phase 3**: Symfony 3.4 → 6.4 LTS アップグレード（大幅な変更のため慎重に実施）
4. **Phase 4**: OS移行（CentOS 7 → AlmaLinux 9）

**注意**: Symfony 3.4から6.4への移行は破壊的変更を含むため、十分な検証期間を確保する。

#### 4.3.3 想定期間
- バージョンアップ期間: 想定2ヶ月（PostgreSQL、PHP環境構築）
- 改修期間: 想定2ヶ月（Symfony 3.4→6.4移行対応、破壊的変更への対応）
- 検証・テスト期間: 想定1ヶ月（Symfony大幅アップグレードのため十分な検証）
- 本番適用期間: 想定2週間
- **合計**: 想定5ヶ月

**注意**: 実際の期間は技術的課題や業務要件により変動する可能性があります。

## 5. 全体スケジュール

### 5.1 並行実施可能性
各システムは独立しているため、リソースが確保できれば並行実施可能。

### 5.2 推奨実施順序（想定工数の大きい順）
1. **FDN、MOC、BPNシステム**（業務機能が最も大規模、想定6ヶ月）
2. **FOCシステム**（Djangoアップグレード含む、想定6ヶ月）
3. **きよらかシステム**（Symfonyアップグレード含む、想定5ヶ月）

**注意**: 上記は想定工数であり、実際の作業では技術的課題や要件変更により期間が変動する可能性があります。

### 5.3 全体期間（想定）
- 順次実施の場合: 想定17ヶ月
- 並行実施の場合: 想定6ヶ月（最長のFDN、MOC、BPNシステムとFOCシステムに合わせる）

## 6. リスクと対策

### 6.1 主要リスク
1. **アプリケーション互換性問題**
2. **データ移行時の不整合**
3. **ダウンタイムの発生**
4. **性能劣化**

### 6.2 対策
1. **十分な検証環境での事前テスト**
2. **段階的なロールバック計画の策定**
3. **メンテナンス時間の確保**
4. **性能監視とチューニング**

## 7. 今後の課題

### 7.1 今後の技術刷新検討事項
現在更新を見送るフレームワークについても、中長期的には以下の対応が必要：

- **Zend Framework**: Laminas Projectへの移行またはLaravel等への技術刷新
- **Backbone.js**: React、Vue.js、Angular等のモダンJavaScriptフレームワークへの移行
- **Angular 10**: Angular 18等への移行またはReact、Vue.js等への技術刷新

### 7.2 フレームワーク移行時の注意点
今回実施するDjangoとSymfonyのアップグレードについては、以下の点に注意が必要：

#### Django 3.1 → 4.2 LTS 移行時の主な変更点
- 非同期ビュー（async views）のサポート
- モデルフィールドの新しいデフォルト値
- URLパターンの変更
- 廃止予定機能の削除

#### Symfony 3.4 → 6.4 LTS 移行時の主な変更点
- PHP 8.1以上が必須
- Flexレシピシステムの導入
- コンポーネント構造の変更
- 設定ファイル形式の変更

### 7.3 技術刷新プロジェクトの検討
Zend FrameworkとBackbone.jsについては、抜本的なシステム刷新について、別途プロジェクトとして検討することを推奨。

---

**作成日**: 2025年7月22日  
**更新日**: -  
**承認者**: -
