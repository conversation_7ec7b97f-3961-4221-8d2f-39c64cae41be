# ミドルウェアバージョンアップ計画書

## 1. 現状システム構成一覧

### 1.1 FDN、BPN、MOCシステム
- **OS**: CentOS 7
- **言語**: PHP 7
- **データベース**: PostgreSQL 10

### 1.2 FOCシステム
- **OS**: CentOS 7
- **言語**: Python 3.8
- **フレームワーク**: Django 3.1
- **データベース**: PostgreSQL 12

### 1.3 きよらかシステム
- **OS**: CentOS 7
- **言語**: PHP 7
- **フレームワーク**: Symfony 3.4
- **データベース**: PostgreSQL 9

## 2. 現状の課題とリスク

### 2.1 セキュリティリスク
- **CentOS 7**: 2024年6月30日でEOL（End of Life）済み
- **PHP 7**: 2022年11月28日でセキュリティサポート終了済み
- **PostgreSQL 9**: 2021年11月11日でEOL済み
- **PostgreSQL 10**: 2022年11月10日でEOL済み

### 2.2 技術的負債
- 古いバージョンによる新機能の利用不可
- セキュリティパッチの提供停止
- 開発・運用効率の低下

## 3. バージョンアップ方針

### 3.1 基本方針
最低限必要なセキュリティサポート対象バージョンへの更新を優先し、システムの安定性を保ちながら段階的にアップグレードを実施する。

### 3.2 フレームワーク更新に関する制約
**重要**: 以下のフレームワークについては、後続バージョンが存在しないか、大幅な仕様変更により新しい技術でのリプレースが必要となるため、**フレームワーク自体の更新は実施しない**。

- **Django 3.1**: Django 4.x系への移行は大幅な仕様変更を伴う
- **Symfony 3.4**: Symfony 4.x/5.x/6.x系への移行は影響範囲が膨大

これらのフレームワーク更新は、システム全体のリプレースプロジェクトとして別途検討が必要。

## 4. 具体的なバージョンアップ計画

### 4.1 FDN、BPN、MOCシステム

#### 4.1.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9 または Rocky Linux 9
- **PHP**: PHP 7 → PHP 8.1 LTS
- **PostgreSQL**: PostgreSQL 10 → PostgreSQL 15

#### 4.1.2 実施手順
1. **Phase 1**: PostgreSQL 10 → 15 アップグレード
2. **Phase 2**: PHP 7 → 8.1 アップグレード（互換性検証含む）
3. **Phase 3**: OS移行（CentOS 7 → AlmaLinux 9）

#### 4.1.3 想定期間
- 検証・テスト期間: 2ヶ月
- 本番適用期間: 1ヶ月
- **合計**: 3ヶ月

### 4.2 FOCシステム

#### 4.2.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9 または Rocky Linux 9
- **Python**: Python 3.8 → Python 3.11
- **Django**: 3.1 → **更新なし**（制約により）
- **PostgreSQL**: PostgreSQL 12 → PostgreSQL 15

#### 4.2.2 実施手順
1. **Phase 1**: PostgreSQL 12 → 15 アップグレード
2. **Phase 2**: Python 3.8 → 3.11 アップグレード（Django 3.1互換性確認）
3. **Phase 3**: OS移行（CentOS 7 → AlmaLinux 9）

#### 4.2.3 想定期間
- 検証・テスト期間: 2ヶ月
- 本番適用期間: 1ヶ月
- **合計**: 3ヶ月

### 4.3 きよらかシステム

#### 4.3.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9 または Rocky Linux 9
- **PHP**: PHP 7 → PHP 8.1 LTS
- **Symfony**: 3.4 → **更新なし**（制約により）
- **PostgreSQL**: PostgreSQL 9 → PostgreSQL 15

#### 4.3.2 実施手順
1. **Phase 1**: PostgreSQL 9 → 15 アップグレード（メジャーバージョンアップのため慎重に実施）
2. **Phase 2**: PHP 7 → 8.1 アップグレード（Symfony 3.4互換性確認）
3. **Phase 3**: OS移行（CentOS 7 → AlmaLinux 9）

#### 4.3.3 想定期間
- 検証・テスト期間: 3ヶ月（PostgreSQL大幅アップグレードのため）
- 本番適用期間: 1ヶ月
- **合計**: 4ヶ月

## 5. 全体スケジュール

### 5.1 並行実施可能性
各システムは独立しているため、リソースが確保できれば並行実施可能。

### 5.2 推奨実施順序
1. **FOCシステム**（PostgreSQL 12→15は比較的影響が少ない）
2. **FDN、BPN、MOCシステム**
3. **きよらかシステム**（PostgreSQL 9→15は最も影響が大きい）

### 5.3 全体期間
- 順次実施の場合: 10ヶ月
- 並行実施の場合: 4ヶ月（最長のきよらかシステムに合わせる）

## 6. リスクと対策

### 6.1 主要リスク
1. **アプリケーション互換性問題**
2. **データ移行時の不整合**
3. **ダウンタイムの発生**
4. **性能劣化**

### 6.2 対策
1. **十分な検証環境での事前テスト**
2. **段階的なロールバック計画の策定**
3. **メンテナンス時間の確保**
4. **性能監視とチューニング**

## 7. 今後の課題

### 7.1 フレームワーク更新の必要性
現在制約により更新を見送るフレームワークについても、中長期的には以下の対応が必要：

- **Django 3.1**: Django 4.x/5.x系への移行またはFastAPI等への技術刷新
- **Symfony 3.4**: Symfony 6.x/7.x系への移行またはLaravel等への技術刷新

### 7.2 技術刷新プロジェクトの検討
フレームワーク更新を含む抜本的なシステム刷新について、別途プロジェクトとして検討することを推奨。

---

**作成日**: 2025年7月22日  
**更新日**: -  
**承認者**: -
