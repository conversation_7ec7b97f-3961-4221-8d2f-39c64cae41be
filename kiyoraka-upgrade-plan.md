---
puppeteer:
  displayHeaderFooter: true
  headerTemplate: "<div style='width: 100%; height: 0;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "1cm"
    bottom: "1.5cm"
    left: "1cm"
  format: "A4"
  printBackground: true
---

# きよらかシステム バージョンアップ計画書

## 目次
1. [現状システム構成](#1-現状システム構成)
2. [現状の課題とリスク](#2-現状の課題とリスク)
3. [バージョンアップ方針](#3-バージョンアップ方針)
4. [具体的なバージョンアップ計画](#4-具体的なバージョンアップ計画)
5. [リスクと対策](#5-リスクと対策)
6. [今後の課題](#6-今後の課題)

## 1. 現状システム構成

### 1.1 システム概要
- **システム名**: きよらかシステム
- **特徴**: Symfony構成のWebアプリケーション

### 1.2 技術スタック
- **OS**: CentOS 7
- **言語**: PHP 7
- **フレームワーク**: Symfony 3.4
- **データベース**: PostgreSQL 9

## 2. 現状の課題とリスク

### 2.1 セキュリティリスク
- **CentOS 7**: 2024年6月30日でEOL（End of Life）済み
- **PHP 7**: 2022年11月28日でセキュリティサポート終了済み
- **PostgreSQL 9**: 2021年11月11日でEOL済み
- **Symfony 3.4**: セキュリティサポートが限定的

### 2.2 技術的負債
- 古いバージョンによる新機能の利用不可
- セキュリティパッチの提供停止
- 開発・運用効率の低下
- Symfony 3.4の保守性低下

## 3. バージョンアップ方針

### 3.1 基本方針
最低限必要なセキュリティサポート対象バージョンへの更新を優先し、システムの安定性を保ちながら段階的にアップグレードを実施する。

### 3.2 フレームワーク更新方針
- **Symfony 3.4**: Symfony 6.4 LTS（サポート期限: 2027年11月）への移行を実施

## 4. 具体的なバージョンアップ計画

**注意**: 以下の想定期間は概算であり、実際の作業では要件の詳細化、予期しない技術的課題、テスト結果等により期間が変動する可能性があります。

### 4.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9（サポート期限: 2032年5月31日）
- **PHP**: PHP 7 → PHP 8.3 LTS（サポート期限: 2026年11月23日）
- **Symfony**: 3.4 → Symfony 6.4 LTS（サポート期限: 2027年11月）
- **PostgreSQL**: PostgreSQL 9 → PostgreSQL 16（サポート期限: 2028年11月9日）

### 4.2 実施手順
1. **Phase 1**: OS移行（CentOS 7 → AlmaLinux 9）
2. **Phase 2**: PostgreSQL 9 → 16 アップグレード（メジャーバージョンアップのため慎重に実施）
3. **Phase 3**: PHP・フレームワークアップグレード（PHP 7 → 8.3、Symfony 3.4 → 6.4 LTS）

**注意**: Symfony 3.4から6.4への移行は破壊的変更を含むため、十分な検証期間を確保する。

### 4.3 想定期間
- **Phase 1 (OS移行)**: 想定3ヶ月
- **Phase 2 (PostgreSQL)**: 想定3ヶ月
- **Phase 3 (PHP・フレームワーク)**: 想定6ヶ月
- **合計**: 想定12ヶ月

**注意**: 実際の期間は技術的課題や業務要件により変動する可能性があります。

### 4.4 詳細スケジュール

#### Phase 1: OS移行（想定3ヶ月）
- **Month 1**: AlmaLinux 9環境構築・基盤設定
- **Month 2**: アプリケーション移行・基本動作確認
- **Month 3**: 性能検証・チューニング

#### Phase 2: PostgreSQL 9 → 16 アップグレード（想定3ヶ月）
- **Month 1**: 環境構築・バックアップ戦略策定
- **Month 2**: PostgreSQL 16インストール・データ移行テスト（大幅バージョンアップのため慎重に）
- **Month 3**: 性能検証・チューニング

#### Phase 3: PHP・フレームワークアップグレード（想定6ヶ月）
- **Month 1**: PHP 8.3環境構築
- **Month 2**: Symfony 6.4環境構築・依存関係更新
- **Month 3-4**: 破壊的変更への対応・コード修正
- **Month 5**: 統合テスト・検証
- **Month 6**: 最終検証・本番適用準備

### 4.5 Symfony移行時の主な変更点
- PHP 8.1以上が必須
- Flexレシピシステムの導入
- コンポーネント構造の変更
- 設定ファイル形式の変更
- セキュリティ機能の強化
- 新しいディレクトリ構造

## 5. リスクと対策

### 5.1 主要リスク
1. **PostgreSQL大幅バージョンアップ**
   - PostgreSQL 9から16への7バージョンアップ
   - データ移行時の不整合リスクが高い
2. **Symfony大幅バージョンアップ**
   - 破壊的変更による動作不良
   - 設定ファイル形式の大幅変更
3. **PHP環境変更**
   - Symfony 6.4とPHP 8.3の互換性
4. **複合的な変更リスク**
   - 複数コンポーネントの同時変更による予期しない影響

### 5.2 対策
1. **十分な検証環境での事前テスト**
2. **段階的なロールバック計画の策定**
3. **Symfony移行ガイドラインの詳細確認**
4. **性能監視とチューニング**

## 6. 今後の課題

### 6.1 継続的なバージョンアップ
今回のアップグレード完了後も、定期的なバージョンアップ計画の策定が必要：

- **Symfony**: 定期的なマイナーバージョンアップ
- **PHP**: セキュリティアップデートの適用
- **PostgreSQL**: 定期的なマイナーバージョンアップ

<!-- ---

**作成日**: 2025年7月22日  
**更新日**: -  
**承認者**: -
