---
puppeteer:
  displayHeaderFooter: true
  headerTemplate: "<div style='width: 100%; height: 0;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "1cm"
    bottom: "1.5cm"
    left: "1cm"
  format: "A4"
  printBackground: true
---

# FOC バージョンアップ計画書

## 目次
1. [現状システム構成](#1-現状システム構成)
2. [現状の課題とリスク](#2-現状の課題とリスク)
3. [バージョンアップ方針](#3-バージョンアップ方針)
4. [具体的なバージョンアップ計画](#4-具体的なバージョンアップ計画)
5. [リスクと対策](#5-リスクと対策)
6. [今後の課題](#6-今後の課題)

## 1. 現状システム構成

### 1.1 システム概要
- **システム名**: FOCシステム
- **特徴**: Django + Angular構成のWebアプリケーション

### 1.2 技術スタック
- **OS**: CentOS 7
- **言語**: Python 3.8
- **Pythonフレームワーク**: Django 3.1
- **JavaScriptフレームワーク**: Angular 10
- **データベース**: PostgreSQL 12

## 2. 現状の課題とリスク

### 2.1 セキュリティリスク
- **CentOS 7**: 2024年6月30日でEOL（End of Life）済み
- **PostgreSQL 12**: 2024年11月14日でEOL済み
- **Django 3.1**: セキュリティサポートが限定的

### 2.2 技術的負債
- 古いバージョンによる新機能の利用不可
- セキュリティパッチの提供停止
- 開発・運用効率の低下
- Angular 10の保守性低下

## 3. バージョンアップ方針

### 3.1 基本方針
最低限必要なセキュリティサポート対象バージョンへの更新を優先し、システムの安定性を保ちながら段階的にアップグレードを実施する。

### 3.2 フレームワーク更新方針

#### 3.2.1 更新対象フレームワーク
- **Django 3.1**: Django 4.2 LTS（サポート期限: 2026年4月）への移行

#### 3.2.2 更新しないフレームワーク
- **Angular 10**: Angular 18等への移行は影響範囲が膨大のため更新しない

## 4. 具体的なバージョンアップ計画

**注意**: 以下の想定期間は概算であり、実際の作業では要件の詳細化、予期しない技術的課題、テスト結果等により期間が変動する可能性があります。

### 4.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9（サポート期限: 2032年5月31日）
- **Python**: Python 3.8 → Python 3.12（サポート期限: 2028年10月）
- **Pythonフレームワーク**: Django 3.1 → Django 4.2 LTS（サポート期限: 2026年4月）
- **JavaScriptフレームワーク**: Angular 10 → **更新なし**（制約により）
- **PostgreSQL**: PostgreSQL 12 → PostgreSQL 16（サポート期限: 2028年11月9日）

### 4.2 実施手順
1. **Phase 1**: PostgreSQL 12 → 16 アップグレード
2. **Phase 2**: Python 3.8 → 3.12 アップグレード
3. **Phase 3**: Django 3.1 → 4.2 LTS アップグレード（大幅な変更のため慎重に実施）
4. **Phase 4**: OS移行（CentOS 7 → AlmaLinux 9）

**注意**: Django 3.1から4.2への移行は破壊的変更を含むため、十分な検証期間を確保する。Angular 10の互換性確認も重要。

### 4.3 想定期間
- バージョンアップ期間: 想定2ヶ月（PostgreSQL、Python環境構築）
- 改修期間: 想定3ヶ月（Django 3.1→4.2移行対応、Angular 10互換性確認）
- 検証・テスト期間: 想定1ヶ月（Django大幅アップグレードのため十分な検証）
- 本番適用期間: 想定2週間
- **合計**: 想定6ヶ月

**注意**: 実際の期間は技術的課題や業務要件により変動する可能性があります。

### 4.4 詳細スケジュール

#### Phase 1: PostgreSQL 12 → 16 アップグレード（想定2ヶ月）
- **Week 1-2**: 環境構築・バックアップ戦略策定
- **Week 3-4**: PostgreSQL 16インストール・設定
- **Week 5-6**: データ移行テスト
- **Week 7-8**: 性能検証・チューニング

#### Phase 2: Python 3.8 → 3.12 アップグレード（想定2週間）
- **Week 1**: Python 3.12環境構築
- **Week 2**: 基本動作確認

#### Phase 3: Django 3.1 → 4.2 LTS アップグレード（想定3ヶ月）
- **Month 1**: Django 4.2環境構築・依存関係更新
- **Month 2**: 破壊的変更への対応・コード修正
- **Month 3**: Angular 10連携部分の検証・修正

#### Phase 4: OS移行（想定1ヶ月）
- **Week 1-2**: AlmaLinux 9環境構築
- **Week 3-4**: アプリケーション移行・最終検証

### 4.5 Django移行時の主な変更点
- 非同期ビュー（async views）のサポート
- モデルフィールドの新しいデフォルト値
- URLパターンの変更
- 廃止予定機能の削除
- セキュリティ機能の強化

## 5. リスクと対策

### 5.1 主要リスク
1. **Django大幅バージョンアップ**
   - 破壊的変更による動作不良
   - APIの変更による影響
2. **Angular 10互換性問題**
   - Django APIとの連携不良
   - フロントエンド機能の動作不良
3. **PostgreSQL移行リスク**
   - データ移行時の不整合
   - 性能劣化
4. **Python環境変更**
   - ライブラリ依存関係の問題

### 5.2 対策
1. **十分な検証環境での事前テスト**
2. **段階的なロールバック計画の策定**
3. **Django移行ガイドラインの詳細確認**
4. **Angular-Django API連携の重点テスト**
5. **性能監視とチューニング**

## 6. 今後の課題

### 6.1 フレームワーク刷新の必要性
中長期的には以下の対応が必要：

- **Angular 10**: Angular 18等への移行またはReact、Vue.js等への技術刷新

### 6.2 技術刷新プロジェクトの検討
Angular更新を含む抜本的なフロントエンド刷新について、別途プロジェクトとして検討することを推奨。

<!-- ---

**作成日**: 2025年7月22日  
**更新日**: -  
**承認者**: -
