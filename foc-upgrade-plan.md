---
puppeteer:
  displayHeaderFooter: true
  headerTemplate: "<div style='width: 100%; height: 0;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "1cm"
    bottom: "1.5cm"
    left: "1cm"
  format: "A4"
  printBackground: true
---

# FOC バージョンアップ計画書

## 目次
1. [現状システム構成](#1-現状システム構成)
2. [現状の課題とリスク](#2-現状の課題とリスク)
3. [バージョンアップ方針](#3-バージョンアップ方針)
4. [具体的なバージョンアップ計画](#4-具体的なバージョンアップ計画)
5. [リスクと対策](#5-リスクと対策)
6. [今後の課題](#6-今後の課題)

## 1. 現状システム構成

### 1.1 システム概要
- **システム名**: FOCシステム
- **特徴**: Django + Angular構成のWebアプリケーション

### 1.2 技術スタック
- **OS**: CentOS 7
- **言語**: Python 3.8
- **Pythonフレームワーク**: Django 3.1
- **JavaScriptフレームワーク**: Angular 10
- **データベース**: PostgreSQL 12

## 2. 現状の課題とリスク

### 2.1 セキュリティリスク
- **CentOS 7**: 2024年6月30日でEOL（End of Life）済み
- **PostgreSQL 12**: 2024年11月14日でEOL済み
- **Django 3.1**: セキュリティサポートが限定的

### 2.2 技術的負債
- 古いバージョンによる新機能の利用不可
- セキュリティパッチの提供停止
- 開発・運用効率の低下
- Angular 10の保守性低下

## 3. バージョンアップ方針

### 3.1 基本方針
最低限必要なセキュリティサポート対象バージョンへの更新を優先し、システムの安定性を保ちながら段階的にアップグレードを実施する。

### 3.2 フレームワーク更新方針

#### 3.2.1 更新対象フレームワーク
- **Django 3.1**: Django 4.2 LTS（サポート期限: 2026年4月）への移行
- **Angular 10**: Angular 18 LTS（サポート期限: 2027年5月）への移行

## 4. 具体的なバージョンアップ計画

**注意**: 以下の想定期間は概算であり、実際の作業では要件の詳細化、予期しない技術的課題、テスト結果等により期間が変動する可能性があります。

### 4.1 対象バージョン
- **OS**: CentOS 7 → AlmaLinux 9（サポート期限: 2032年5月31日）
- **Python**: Python 3.8 → Python 3.12（サポート期限: 2028年10月）
- **Pythonフレームワーク**: Django 3.1 → Django 4.2 LTS（サポート期限: 2026年4月）
- **JavaScriptフレームワーク**: Angular 10 → Angular 18 LTS（サポート期限: 2027年5月）
- **PostgreSQL**: PostgreSQL 12 → PostgreSQL 16（サポート期限: 2028年11月9日）

### 4.2 実施手順
1. **Phase 1**: OS移行（CentOS 7 → AlmaLinux 9）
2. **Phase 2**: PostgreSQL 12 → 16 アップグレード
3. **Phase 3**: Pythonフレームワークアップグレード（Python 3.8 → 3.12、Django 3.1 → 4.2 LTS、Angular 10 → 18 LTS）

**注意**: Django 3.1から4.2、Angular 10から18への移行は破壊的変更を含むため、十分な検証期間を確保する。特にAngular-Django API連携部分の検証が重要。

### 4.3 想定期間
- **Phase 1 (OS移行)**: 想定3ヶ月
- **Phase 2 (PostgreSQL)**: 想定3ヶ月
- **Phase 3 (Python・JavaScriptフレームワーク)**: 想定6ヶ月（Angular大幅アップグレード含む）
- **合計**: 想定12ヶ月

**注意**: 実際の期間は技術的課題や業務要件により変動する可能性があります。

### 4.4 詳細スケジュール

#### Phase 1: OS移行（想定3ヶ月）
- **Month 1**: AlmaLinux 9環境構築・基盤設定
- **Month 2**: アプリケーション移行・基本動作確認
- **Month 3**: 性能検証・チューニング

#### Phase 2: PostgreSQL 12 → 16 アップグレード（想定3ヶ月）
- **Month 1**: 環境構築・バックアップ戦略策定
- **Month 2**: PostgreSQL 16インストール・データ移行テスト
- **Month 3**: 性能検証・チューニング

#### Phase 3: Python・JavaScriptフレームワークアップグレード（想定6ヶ月）
- **Month 1**: Python 3.12環境構築
- **Month 2**: Django 4.2環境構築・依存関係更新
- **Month 3**: Django破壊的変更への対応・コード修正
- **Month 4**: Angular 18 LTS環境構築・依存関係更新
- **Month 5**: Angular破壊的変更への対応・コード修正
- **Month 6**: Angular-Django API連携部分の検証・統合テスト

### 4.5 フレームワーク移行時の主な変更点

#### Django 3.1 → 4.2 LTS 移行時の変更点
- 非同期ビュー（async views）のサポート
- モデルフィールドの新しいデフォルト値
- URLパターンの変更
- 廃止予定機能の削除
- セキュリティ機能の強化

#### Angular 10 → 18 LTS 移行時の変更点
- Node.js 18以上が必須
- TypeScript 4.9以上が必須
- Ivy レンダリングエンジンの完全移行
- Angular Material の大幅な変更
- 新しいコントロールフロー構文（@if、@for等）
- Standalone Components の導入
- 新しいライフサイクルフック

## 5. リスクと対策

### 5.1 主要リスク
1. **Django大幅バージョンアップ**
   - 破壊的変更による動作不良
   - APIの変更による影響
2. **Angular大幅バージョンアップ**
   - Angular 10から18への8バージョンアップ
   - TypeScript、Node.js環境の大幅変更
   - コンポーネント構造の変更
   - ライブラリ依存関係の大幅な変更
3. **フロントエンド・バックエンド連携リスク**
   - Django APIとAngularの連携不良
   - 認証・セッション管理の変更
4. **PostgreSQL移行リスク**
   - データ移行時の不整合
   - 性能劣化
5. **Python環境変更**
   - ライブラリ依存関係の問題

### 5.2 対策
1. **十分な検証環境での事前テスト**
2. **段階的なロールバック計画の策定**
3. **Django移行ガイドラインの詳細確認**
4. **Angular移行ガイドラインの詳細確認**
5. **Angular-Django API連携の重点テスト**
6. **フロントエンド専門技術者による支援**
7. **性能監視とチューニング**

## 6. 今後の課題

### 6.1 継続的なバージョンアップ
今回のアップグレード完了後も、定期的なバージョンアップ計画の策定が必要：

- **Django**: 定期的なマイナーバージョンアップ
- **Angular**: 定期的なマイナーバージョンアップ
- **Python**: セキュリティアップデートの適用
- **PostgreSQL**: 定期的なマイナーバージョンアップ



<!-- ---

**作成日**: 2025年7月22日  
**更新日**: -  
**承認者**: -
